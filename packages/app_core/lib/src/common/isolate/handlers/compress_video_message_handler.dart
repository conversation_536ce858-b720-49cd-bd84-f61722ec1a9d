import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/widgets.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';
import 'package:video_compressor/video_compressor.dart';

import '../../../../core.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../output/worker_compress_video_output.dart';

@pragma("vm:entry-point")
class CompressVideoHandler {
  /// Public API - giữ nguyên interface
  Future<WorkerCompressVideoOutput> compressVideo(
    WorkerCompressVideoInput inputData,
  ) async {
    if (inputData.creationTime
        .add(GlobalConfig.sendTimeoutDuration)
        .isBefore(DateTime.now())) {
      throw SendMessageTimeoutException(msgRef: inputData.file.ref);
    }

    final receivePort = ReceivePort();
    try {
      await FlutterIsolate.spawn(
        _compressVideoEntry,
        [receivePort.sendPort, jsonEncode(inputData.toJson())],
      );

      final outputJson = await receivePort.first as String;
      final resultMap = jsonDecode(outputJson) as Map<String, dynamic>;

      if (resultMap.containsKey('error')) {
        throw Exception(resultMap['error']);
      }

      return WorkerCompressVideoOutput.fromJson(resultMap);
    } finally {
      receivePort.close();
    }
  }

  @pragma("vm:entry-point")
  static Future<void> _compressVideoEntry(List<dynamic> args) async {
    WidgetsFlutterBinding.ensureInitialized();

    final sendPort = args[0] as SendPort;
    final inputMap = jsonDecode(args[1] as String) as Map<String, dynamic>;
    final inputData = WorkerCompressVideoInput.fromJson(inputMap);

    try {
      final output = await _compressVideoInternal(inputData);
      sendPort.send(jsonEncode(output.toJson()));
    } catch (e, st) {
      sendPort.send(
        jsonEncode({
          'error': e.toString(),
          'stackTrace': st.toString(),
        }),
      );
    }
  }

  static Future<WorkerCompressVideoOutput> _compressVideoInternal(
    WorkerCompressVideoInput inputData,
  ) async {
    final filePath = inputData.file.path;
    if (!File(filePath).existsSync()) {
      throw FileNotFoundException(message: 'File $filePath not exists');
    }

    final messageRef = inputData.messageRef ?? inputData.file.ref;
    final fileRef = inputData.file.fileRef ?? inputData.file.ref;
    final safeFilePath = _getSafeFilePath(filePath);

    final messageCacheDir =
        await _createMessageCacheDirectory(messageRef, fileRef);
    final videoDir = await _createVideoSubdirectory(messageCacheDir);
    final thumbnailDir = await _createThumbnailSubdirectory(messageCacheDir);

    final originalFileName = path.basename(filePath);
    final thumbnailName =
        '${path.basenameWithoutExtension(originalFileName)}_${fileRef}_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final uniqueThumbnailPath = path.join(thumbnailDir.path, thumbnailName);

    final resultList = await Future.wait([
      VideoCompressor.compressVideo(path: safeFilePath),
      VideoCompressor.getFileThumbnail(
        path: safeFilePath,
        quality: 100,
        position: -1,
      ),
    ]);

    final videoInfo = resultList[0] as VideoInfo?;
    final fileThumbnail = resultList[1] as File;

    String compressedVideoPath = safeFilePath;

    if (videoInfo?.file != null) {
      final originalVideoName = path.basename(videoInfo!.file!.path);
      final compressedVideoName =
          _createUniqueFileName(originalVideoName, fileRef);
      final uniqueCompressedVideoPath =
          path.join(videoDir.path, compressedVideoName);
      try {
        videoInfo.file!.copySync(uniqueCompressedVideoPath);
        compressedVideoPath = uniqueCompressedVideoPath;
      } catch (_) {
        compressedVideoPath = videoInfo.file!.path;
      }
    }

    if (fileThumbnail.existsSync()) {
      try {
        fileThumbnail.copySync(uniqueThumbnailPath);
      } catch (_) {}
    }

    return WorkerCompressVideoOutput(
      videoPath: compressedVideoPath,
      thumbnailPath: fileThumbnail.existsSync() ? uniqueThumbnailPath : '',
      duration: videoInfo?.duration?.toInt() ?? 0,
    );
  }

  /// Helpers (giữ nguyên logic cũ)
  static String _getSafeFilePath(String path) {
    return path.replaceAll('file://', '');
  }

  static Future<Directory> _createMessageCacheDirectory(
    String messageRef,
    String fileRef,
  ) async {
    final cacheDir =
        await Directory('${Directory.systemTemp.path}/msg_$messageRef')
            .create(recursive: true);
    return Directory('${cacheDir.path}/$fileRef')..createSync(recursive: true);
  }

  static Future<Directory> _createVideoSubdirectory(Directory parent) async {
    return Directory('${parent.path}/videos')..createSync(recursive: true);
  }

  static Future<Directory> _createThumbnailSubdirectory(
    Directory parent,
  ) async {
    return Directory('${parent.path}/thumbnails')..createSync(recursive: true);
  }

  static String _createUniqueFileName(String base, String ref) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final name = path.basenameWithoutExtension(base);
    final ext = path.extension(base);
    return '${name}_$ref\_$timestamp$ext';
  }
}
